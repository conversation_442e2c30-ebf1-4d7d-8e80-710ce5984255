<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ابدأ التشخيص - مساعد طبي ذكي</title>
    <meta name="description" content="اوصف أعراضك واحصل على تحليل وتشخيص بالذكاء الاصطناعي">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap RTL -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css">
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;500;600;700&display=swap" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="style-bootstrap.css">
    <style>
        body { font-family: 'Cairo', sans-serif; }

        /* Chat Container Styles */
        .chat-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .chat-header {
            background: linear-gradient(135deg, #007bff, #0056b3) !important;
        }

        .quick-action-btn {
            padding: 1rem;
            height: auto;
            border-radius: 15px;
            transition: all 0.3s ease;
        }

        .quick-action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 123, 255, 0.3);
        }

        .quick-action-btn i {
            font-size: 1.5rem;
        }

        /* Message Styles */
        .message {
            margin-bottom: 1rem;
            display: flex;
            align-items: flex-start;
            gap: 0.75rem;
        }

        .message.user {
            flex-direction: row-reverse;
        }

        .message-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            color: white;
            flex-shrink: 0;
        }

        .message.user .message-avatar {
            background: linear-gradient(45deg, #007bff, #0056b3);
        }

        .message.bot .message-avatar {
            background: linear-gradient(45deg, #28a745, #1e7e34);
        }

        .message-content {
            background: #f8f9fa;
            padding: 0.75rem 1rem;
            border-radius: 18px;
            max-width: 70%;
            position: relative;
            line-height: 1.6;
            word-wrap: break-word;
        }

        .message.user .message-content {
            background: linear-gradient(45deg, #007bff, #0056b3);
            color: white;
        }

        .message-time {
            font-size: 0.75rem;
            color: #6c757d;
            margin-top: 0.25rem;
        }

        .message.user .message-time {
            color: rgba(255, 255, 255, 0.8);
        }

        /* Typing Indicator */
        .typing-dots {
            display: flex;
            gap: 4px;
        }

        .typing-dots span {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #007bff;
            animation: typing 1.4s infinite ease-in-out;
        }

        .typing-dots span:nth-child(1) { animation-delay: -0.32s; }
        .typing-dots span:nth-child(2) { animation-delay: -0.16s; }

        @keyframes typing {
            0%, 80%, 100% { transform: scale(0); opacity: 0.5; }
            40% { transform: scale(1); opacity: 1; }
        }

        /* Input Styles */
        #messageInput {
            border: 2px solid #e9ecef;
            border-radius: 25px;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
        }

        #messageInput:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }

        #sendBtn {
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        #sendBtn:not(:disabled):hover {
            transform: scale(1.1);
            box-shadow: 0 5px 15px rgba(0, 123, 255, 0.4);
        }

        #sendBtn:disabled {
            background-color: #e9ecef;
            border-color: #e9ecef;
            color: #6c757d;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .message-content {
                max-width: 85%;
            }

            .quick-action-btn {
                padding: 0.75rem;
            }

            .quick-action-btn i {
                font-size: 1.25rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-light fixed-top shadow-sm">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="index.html">
                <i class="fas fa-heartbeat text-primary me-2"></i>
                <span class="fw-bold">مساعد طبي ذكي</span>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="diagnosis.html">التشخيص</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="appointments.html">حجز موعد</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    
    <!-- Main Content -->
    <main class="py-5 py-md-6 mt-5">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-10">
                    <!-- Medical Chatbot Interface -->
                    <div class="chat-container bg-white rounded-4 shadow-lg overflow-hidden">
                        <!-- Chat Header -->
                        <div class="chat-header bg-primary text-white p-4">
                            <div class="d-flex align-items-center justify-content-between">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-robot fs-2 me-3"></i>
                                    <div>
                                        <h2 class="h4 mb-1">المساعد الطبي الذكي</h2>
                                        <p class="mb-0 opacity-75">اسأل عن أعراضك واحصل على مساعدة فورية</p>
                                    </div>
                                </div>
                                <div class="d-flex gap-2">
                                    <button class="btn btn-outline-light btn-sm" id="clearChatBtn" title="مسح المحادثة">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    <button class="btn btn-outline-light btn-sm" id="exportChatBtn" title="تصدير المحادثة">
                                        <i class="fas fa-download"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Welcome Message -->
                        <div class="welcome-section p-5 text-center border-bottom" id="welcomeSection">
                            <div class="mb-4">
                                <i class="fas fa-stethoscope text-primary" style="font-size: 4rem;"></i>
                            </div>
                            <h3 class="h4 mb-3">مرحباً بك في المساعد الطبي الذكي</h3>
                            <p class="text-muted mb-4">أنا هنا لمساعدتك في الأسئلة الطبية والإسعافات الأولية</p>

                            <!-- Quick Action Buttons -->
                            <div class="row g-3">
                                <div class="col-md-3 col-6">
                                    <button class="btn btn-outline-primary w-100 quick-action-btn" data-message="أعاني من صداع شديد">
                                        <i class="fas fa-head-side-virus d-block mb-2"></i>
                                        <small>صداع</small>
                                    </button>
                                </div>
                                <div class="col-md-3 col-6">
                                    <button class="btn btn-outline-primary w-100 quick-action-btn" data-message="عندي حمى وأعراض برد">
                                        <i class="fas fa-thermometer-half d-block mb-2"></i>
                                        <small>حمى</small>
                                    </button>
                                </div>
                                <div class="col-md-3 col-6">
                                    <button class="btn btn-outline-primary w-100 quick-action-btn" data-message="أعمل إيه لو حد اغمى عليه؟">
                                        <i class="fas fa-dizzy d-block mb-2"></i>
                                        <small>إغماء</small>
                                    </button>
                                </div>
                                <div class="col-md-3 col-6">
                                    <button class="btn btn-outline-primary w-100 quick-action-btn" data-message="إزاي أتعامل مع الحروق؟">
                                        <i class="fas fa-fire d-block mb-2"></i>
                                        <small>حروق</small>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Chat Messages Area -->
                        <div class="chat-messages p-4" id="chatMessages" style="min-height: 400px; max-height: 500px; overflow-y: auto;">
                            <!-- Messages will be added here dynamically -->
                        </div>

                        <!-- Typing Indicator -->
                        <div class="typing-indicator px-4 pb-3" id="typingIndicator" style="display: none;">
                            <div class="d-flex align-items-center text-muted">
                                <div class="typing-dots me-2">
                                    <span></span>
                                    <span></span>
                                    <span></span>
                                </div>
                                <small>المساعد الطبي يكتب...</small>
                            </div>
                        </div>

                        <!-- Chat Input -->
                        <div class="chat-input border-top p-4">
                            <div class="input-group">
                                <textarea
                                    class="form-control border-0 shadow-none"
                                    id="messageInput"
                                    placeholder="اكتب سؤالك الطبي هنا... (مثال: عندي صداع شديد)"
                                    rows="1"
                                    style="resize: none; max-height: 120px;"
                                ></textarea>
                                <button class="btn btn-primary px-4" id="sendBtn" disabled>
                                    <i class="fas fa-paper-plane"></i>
                                </button>
                            </div>
                            <div class="mt-2">
                                <small class="text-muted">
                                    <i class="fas fa-exclamation-triangle me-1"></i>
                                    هذا المساعد لا يغني عن استشارة الطبيب المختص
                                </small>
                            </div>
                        </div>
                    </div>

                    <!-- Tips Card -->
                    <div class="card border-0 shadow-sm rounded-4 mt-4">
                        <div class="card-body p-4">
                            <h5 class="text-primary fw-bold mb-3">
                                <i class="fas fa-lightbulb me-2"></i>
                                نصائح للحصول على أفضل مساعدة:
                            </h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <ul class="list-unstyled">
                                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>كن محدداً في وصف أعراضك</li>
                                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>اذكر متى بدأت الأعراض</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <ul class="list-unstyled">
                                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>اذكر أي تاريخ طبي ذي صلة</li>
                                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>صف شدة الأعراض</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Emergency Modal -->
    <div class="modal fade" id="emergencyModal" tabindex="-1" aria-labelledby="emergencyModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title" id="emergencyModalLabel">
                        <i class="fas fa-ambulance me-2"></i>
                        حالة طوارئ؟
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p class="mb-3">إذا كانت هذه حالة طوارئ طبية، يرجى الاتصال فوراً بـ:</p>
                    <div class="row g-3">
                        <div class="col-6">
                            <div class="d-flex align-items-center p-3 bg-light rounded">
                                <i class="fas fa-phone text-danger fs-4 me-3"></i>
                                <div>
                                    <strong>الإسعاف</strong>
                                    <div class="text-danger fw-bold">123</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="d-flex align-items-center p-3 bg-light rounded">
                                <i class="fas fa-hospital text-danger fs-4 me-3"></i>
                                <div>
                                    <strong>الطوارئ</strong>
                                    <div class="text-danger fw-bold">16123</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" data-bs-dismiss="modal">متابعة المحادثة</button>
                </div>
            </div>
        </div>
    </div>
    


    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Arabic Localization -->
    <script src="js/arabic-localization.js"></script>
    <!-- Custom JS -->
    <script src="js/main-bootstrap.js"></script>
    <script>
        // Medical Chatbot JavaScript
        document.addEventListener('DOMContentLoaded', function() {
            // DOM Elements
            const chatMessages = document.getElementById('chatMessages');
            const messageInput = document.getElementById('messageInput');
            const sendBtn = document.getElementById('sendBtn');
            const welcomeSection = document.getElementById('welcomeSection');
            const typingIndicator = document.getElementById('typingIndicator');
            const clearChatBtn = document.getElementById('clearChatBtn');
            const exportChatBtn = document.getElementById('exportChatBtn');
            const emergencyModal = new bootstrap.Modal(document.getElementById('emergencyModal'));

            // Chat state
            let chatHistory = [];
            let isTyping = false;

            // Emergency keywords that trigger the emergency modal
            const emergencyKeywords = [
                'طوارئ', 'emergency', 'نزيف', 'bleeding', 'اختناق', 'choking',
                'توقف القلب', 'cardiac arrest', 'فقدان الوعي', 'unconscious',
                'حادث', 'accident', 'سكتة', 'stroke', 'نوبة قلبية', 'heart attack',
                'تسمم', 'poisoning', 'حريق', 'fire', 'غرق', 'drowning'
            ];

            // Initialize event listeners
            initializeEventListeners();
            loadChatHistory();
            adjustTextareaHeight();

            function initializeEventListeners() {
                // Send message events
                sendBtn.addEventListener('click', sendMessage);
                messageInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        sendMessage();
                    }
                });

                // Input validation
                messageInput.addEventListener('input', function() {
                    const hasText = this.value.trim().length > 0;
                    sendBtn.disabled = !hasText;
                    adjustTextareaHeight();

                    // Check for emergency keywords
                    if (hasText && containsEmergencyKeyword(this.value)) {
                        emergencyModal.show();
                    }
                });

                // Quick action buttons
                document.querySelectorAll('.quick-action-btn').forEach(btn => {
                    btn.addEventListener('click', function() {
                        const message = this.getAttribute('data-message');
                        messageInput.value = message;
                        sendBtn.disabled = false;
                        sendMessage();
                    });
                });

                // Clear chat
                clearChatBtn.addEventListener('click', clearChat);

                // Export chat
                exportChatBtn.addEventListener('click', exportChat);
            }

            // Send message function
            async function sendMessage() {
                const message = messageInput.value.trim();
                if (!message || isTyping) return;

                // Add user message to chat
                addMessage(message, 'user');
                messageInput.value = '';
                sendBtn.disabled = true;
                adjustTextareaHeight();

                // Hide welcome message
                if (welcomeSection) {
                    welcomeSection.style.display = 'none';
                }

                // Show typing indicator
                showTypingIndicator();

                try {
                    // Send message to backend
                    const response = await fetch('/chat', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ message: message })
                    });

                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }

                    const data = await response.json();

                    // Hide typing indicator
                    hideTypingIndicator();

                    // Add bot response
                    addMessage(data.response, 'bot');

                } catch (error) {
                    console.error('Error:', error);
                    hideTypingIndicator();

                    // Add error message
                    addMessage('عذراً، حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى.', 'bot', true);
                }

                // Save chat history
                saveChatHistory();
            }

            // Add message to chat
            function addMessage(text, sender, isError = false) {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${sender}`;

                const avatar = document.createElement('div');
                avatar.className = 'message-avatar';
                avatar.innerHTML = sender === 'user' ? '<i class="fas fa-user"></i>' : '<i class="fas fa-robot"></i>';

                const content = document.createElement('div');
                content.className = 'message-content';

                if (isError) {
                    content.style.background = 'linear-gradient(45deg, #dc3545, #c82333)';
                    content.style.color = 'white';
                }

                // Format message text (convert line breaks, etc.)
                content.innerHTML = formatMessageText(text);

                const time = document.createElement('div');
                time.className = 'message-time';
                time.textContent = new Date().toLocaleTimeString('ar-EG', {
                    hour: '2-digit',
                    minute: '2-digit'
                });

                content.appendChild(time);
                messageDiv.appendChild(avatar);
                messageDiv.appendChild(content);

                chatMessages.appendChild(messageDiv);

                // Scroll to bottom
                chatMessages.scrollTop = chatMessages.scrollHeight;

                // Add to chat history
                chatHistory.push({
                    text: text,
                    sender: sender,
                    timestamp: new Date().toISOString(),
                    isError: isError
                });

                // Animate message appearance
                messageDiv.style.opacity = '0';
                messageDiv.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    messageDiv.style.transition = 'all 0.3s ease';
                    messageDiv.style.opacity = '1';
                    messageDiv.style.transform = 'translateY(0)';
                }, 100);
            }

            // Format message text
            function formatMessageText(text) {
                return text
                    .replace(/\n/g, '<br>')
                    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                    .replace(/\*(.*?)\*/g, '<em>$1</em>')
                    .replace(/(https?:\/\/[^\s]+)/g, '<a href="$1" target="_blank">$1</a>');
            }

            // Show/Hide typing indicator
            function showTypingIndicator() {
                isTyping = true;
                typingIndicator.style.display = 'block';
                chatMessages.scrollTop = chatMessages.scrollHeight;
            }

            function hideTypingIndicator() {
                isTyping = false;
                typingIndicator.style.display = 'none';
            }

            // Adjust textarea height
            function adjustTextareaHeight() {
                messageInput.style.height = 'auto';
                messageInput.style.height = Math.min(messageInput.scrollHeight, 120) + 'px';
            }

            // Emergency modal functions
            function containsEmergencyKeyword(text) {
                const lowerText = text.toLowerCase();
                return emergencyKeywords.some(keyword => lowerText.includes(keyword));
            }

            // Clear chat function
            function clearChat() {
                if (confirm('هل أنت متأكد من مسح المحادثة؟')) {
                    chatMessages.innerHTML = '';
                    chatHistory = [];
                    welcomeSection.style.display = 'block';
                    localStorage.removeItem('medicalChatHistory');
                }
            }

            // Export chat function
            function exportChat() {
                if (chatHistory.length === 0) {
                    alert('لا توجد محادثة لتصديرها');
                    return;
                }

                const chatText = chatHistory.map(msg => {
                    const sender = msg.sender === 'user' ? 'المستخدم' : 'المساعد الطبي';
                    const time = new Date(msg.timestamp).toLocaleString('ar-EG');
                    return `[${time}] ${sender}: ${msg.text}`;
                }).join('\n\n');

                const blob = new Blob([chatText], { type: 'text/plain;charset=utf-8' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `medical-chat-${new Date().toISOString().split('T')[0]}.txt`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            }

            // Save/Load chat history
            function saveChatHistory() {
                localStorage.setItem('medicalChatHistory', JSON.stringify(chatHistory));
            }

            function loadChatHistory() {
                const saved = localStorage.getItem('medicalChatHistory');
                if (saved) {
                    chatHistory = JSON.parse(saved);

                    // Restore messages
                    chatHistory.forEach(msg => {
                        addMessageToDOM(msg.text, msg.sender, msg.isError);
                    });

                    if (chatHistory.length > 0) {
                        welcomeSection.style.display = 'none';
                    }
                }
            }

            // Add message to DOM without adding to history (for loading saved messages)
            function addMessageToDOM(text, sender, isError = false) {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${sender}`;

                const avatar = document.createElement('div');
                avatar.className = 'message-avatar';
                avatar.innerHTML = sender === 'user' ? '<i class="fas fa-user"></i>' : '<i class="fas fa-robot"></i>';

                const content = document.createElement('div');
                content.className = 'message-content';

                if (isError) {
                    content.style.background = 'linear-gradient(45deg, #dc3545, #c82333)';
                    content.style.color = 'white';
                }

                content.innerHTML = formatMessageText(text);

                messageDiv.appendChild(avatar);
                messageDiv.appendChild(content);
                chatMessages.appendChild(messageDiv);
            }
        });
    </script>
</body>
</html>
