/**
 * إصلاح شامل لنظام حجز الأطباء
 * يحل جميع المشاكل المتعلقة بالتنقل والبيانات والتفاعل
 */

// بيانات الأطباء الموحدة
const DOCTORS_DATABASE = {
    1: { name: 'د. أحمد محمد', image: 'https://images.unsplash.com/photo-1612349317150-e413f6a5b16d?w=150&h=150&fit=crop&crop=face', price: 300, specialty: 'أمراض القلب', rating: 4.8, experience: 15 },
    2: { name: 'د. فاطمة علي', image: 'https://images.unsplash.com/photo-1559839734-2b71ea197ec2?w=150&h=150&fit=crop&crop=face', price: 350, specialty: 'أمراض القلب', rating: 4.9, experience: 12 },
    3: { name: 'د. محمد حسن', image: 'https://images.unsplash.com/photo-1582750433449-648ed127bb54?w=150&h=150&fit=crop&crop=face', price: 400, specialty: 'أمراض القلب', rating: 4.7, experience: 18 },
    4: { name: 'د. سارة أحمد', image: 'https://images.unsplash.com/photo-1594824475317-d3c5b8b8e8e8?w=150&h=150&fit=crop&crop=face', price: 450, specialty: 'أمراض الأعصاب', rating: 4.9, experience: 20 },
    5: { name: 'د. عمر محمود', image: 'https://images.unsplash.com/photo-1607990281513-2c110a25bd8c?w=150&h=150&fit=crop&crop=face', price: 380, specialty: 'أمراض الأعصاب', rating: 4.6, experience: 14 },
    6: { name: 'د. ليلى حسام', image: 'https://images.unsplash.com/photo-1594824475317-d3c5b8b8e8e8?w=150&h=150&fit=crop&crop=face', price: 320, specialty: 'العظام', rating: 4.8, experience: 16 },
    7: { name: 'د. كريم فؤاد', image: 'https://images.unsplash.com/photo-1612349317150-e413f6a5b16d?w=150&h=150&fit=crop&crop=face', price: 280, specialty: 'العظام', rating: 4.7, experience: 11 },
    8: { name: 'د. نور الهدى', image: 'https://images.unsplash.com/photo-1559839734-2b71ea197ec2?w=150&h=150&fit=crop&crop=face', price: 250, specialty: 'الجلدية', rating: 4.9, experience: 13 },
    9: { name: 'د. حسام الدين', image: 'https://images.unsplash.com/photo-1582750433449-648ed127bb54?w=150&h=150&fit=crop&crop=face', price: 220, specialty: 'الجلدية', rating: 4.5, experience: 9 },
    10: { name: 'د. منى سالم', image: 'https://images.unsplash.com/photo-1594824475317-d3c5b8b8e8e8?w=150&h=150&fit=crop&crop=face', price: 200, specialty: 'طب الأطفال', rating: 4.8, experience: 17 },
    11: { name: 'د. يوسف عادل', image: 'https://images.unsplash.com/photo-1607990281513-2c110a25bd8c?w=150&h=150&fit=crop&crop=face', price: 180, specialty: 'طب الأطفال', rating: 4.6, experience: 8 },
    12: { name: 'د. رانيا طارق', image: 'https://images.unsplash.com/photo-1559839734-2b71ea197ec2?w=150&h=150&fit=crop&crop=face', price: 150, specialty: 'طب عام', rating: 4.7, experience: 10 },
    13: { name: 'د. خالد نبيل', image: 'https://images.unsplash.com/photo-1612349317150-e413f6a5b16d?w=150&h=150&fit=crop&crop=face', price: 120, specialty: 'طب عام', rating: 4.4, experience: 7 }
};

// بيانات التخصصات
const SPECIALTIES_DATABASE = {
    cardiology: { name: 'أمراض القلب', doctors: [1, 2, 3] },
    neurology: { name: 'أمراض الأعصاب', doctors: [4, 5] },
    orthopedics: { name: 'العظام', doctors: [6, 7] },
    dermatology: { name: 'الجلدية', doctors: [8, 9] },
    pediatrics: { name: 'طب الأطفال', doctors: [10, 11] },
    general: { name: 'طب عام', doctors: [12, 13] }
};

// الأوقات المتاحة
const AVAILABLE_TIMES = [
    '09:00', '09:30', '10:00', '10:30', '11:00', '11:30',
    '12:00', '12:30', '14:00', '14:30', '15:00', '15:30',
    '16:00', '16:30', '17:00', '17:30', '18:00', '18:30'
];

// الأوقات غير المتاحة (عشوائية)
const UNAVAILABLE_TIMES = ['10:30', '14:30', '16:00'];

/**
 * إصلاح شامل لنظام حجز الأطباء
 */
function initializeAppointmentSystem() {
    const currentPage = window.location.pathname.split('/').pop();

    switch (currentPage) {
        case 'appointments.html':
            fixAppointmentsPage();
            break;
        case 'doctors.html':
            fixDoctorsPage();
            break;
        case 'booking.html':
            fixBookingPage();
            break;
        case 'confirmation.html':
            fixConfirmationPage();
            break;
        case 'my-appointments.html':
            fixMyAppointmentsPage();
            break;
    }
}

/**
 * إصلاح صفحة اختيار التخصص
 */
function fixAppointmentsPage() {
    console.log('إصلاح صفحة اختيار التخصص...');

    // التأكد من وجود العناصر
    const specialtyCards = document.querySelectorAll('.specialty-card');
    const nextBtn = document.getElementById('nextBtn');

    if (specialtyCards.length === 0) {
        console.error('لم يتم العثور على بطاقات التخصص');
        return;
    }

    let selectedSpecialty = null;

    // إضافة مستمعي الأحداث مع أنيميشن متقدم
    specialtyCards.forEach((card, index) => {
        // إضافة أنيميشن دخول متدرج للبطاقات
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        card.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';

        setTimeout(() => {
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 150);

        // إضافة تأثير hover متقدم
        card.addEventListener('mouseenter', function() {
            if (!this.classList.contains('selected')) {
                this.style.transform = 'translateY(-8px) scale(1.02)';
                this.style.boxShadow = '0 15px 35px rgba(0,0,0,0.1)';
            }
        });

        card.addEventListener('mouseleave', function() {
            if (!this.classList.contains('selected')) {
                this.style.transform = 'translateY(0) scale(1)';
                this.style.boxShadow = '';
            }
        });

        card.addEventListener('click', function() {
            // إزالة التحديد السابق مع أنيميشن
            specialtyCards.forEach(c => {
                c.classList.remove('border-primary', 'bg-light', 'selected');
                c.style.transform = 'translateY(0) scale(1)';
                c.style.boxShadow = '';
                c.style.borderColor = '';

                // إزالة أيقونة الاختيار مع أنيميشن
                const checkIcon = c.querySelector('.check-icon');
                if (checkIcon) {
                    checkIcon.style.animation = 'fadeOut 0.3s ease-out forwards';
                    setTimeout(() => checkIcon.remove(), 300);
                }

                // إزالة تأثير النبض
                const pulseEffect = c.querySelector('.pulse-effect');
                if (pulseEffect) pulseEffect.remove();
            });

            // إضافة التحديد الجديد مع أنيميشن متقدم
            this.classList.add('border-primary', 'bg-light', 'selected');
            this.style.transform = 'translateY(-5px) scale(1.05)';
            this.style.boxShadow = '0 20px 40px rgba(13, 110, 253, 0.2)';
            this.style.borderColor = '#0d6efd';
            this.style.borderWidth = '3px';

            // إضافة تأثير النبض
            const pulseEffect = document.createElement('div');
            pulseEffect.className = 'pulse-effect position-absolute';
            pulseEffect.style.cssText = `
                top: -5px; left: -5px; right: -5px; bottom: -5px;
                border: 2px solid #0d6efd;
                border-radius: 20px;
                animation: pulse 2s infinite;
                pointer-events: none;
                z-index: 1;
            `;
            this.appendChild(pulseEffect);

            // إضافة أيقونة الاختيار مع أنيميشن
            const checkIcon = document.createElement('div');
            checkIcon.className = 'check-icon position-absolute';
            checkIcon.innerHTML = '<i class="fas fa-check-circle text-primary"></i>';
            checkIcon.style.cssText = `
                top: 15px; right: 15px; z-index: 10;
                font-size: 2rem;
                animation: bounceIn 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
                filter: drop-shadow(0 2px 4px rgba(0,0,0,0.2));
            `;
            this.appendChild(checkIcon);

            // إضافة تأثير الاهتزاز الخفيف
            this.style.animation = 'gentleShake 0.5s ease-in-out';
            setTimeout(() => {
                this.style.animation = '';
            }, 500);

            selectedSpecialty = this.dataset.specialty;

            // تفعيل زر التالي مع أنيميشن
            if (nextBtn) {
                nextBtn.disabled = false;
                nextBtn.classList.remove('btn-secondary');
                nextBtn.classList.add('btn-primary');
                nextBtn.style.animation = 'pulse 0.6s ease-in-out';
                setTimeout(() => {
                    nextBtn.style.animation = '';
                }, 600);
            }

            console.log('تم اختيار التخصص:', selectedSpecialty);
        });
    });

    // التعامل مع زر التالي
    if (nextBtn) {
        nextBtn.addEventListener('click', function() {
            if (selectedSpecialty) {
                console.log('الانتقال إلى صفحة الأطباء للتخصص:', selectedSpecialty);
                window.location.href = `doctors.html?specialty=${selectedSpecialty}`;
            } else {
                showAlert('يرجى اختيار التخصص أولاً', 'warning');
            }
        });
    }
}

/**
 * إصلاح صفحة اختيار الطبيب
 */
function fixDoctorsPage() {
    console.log('إصلاح صفحة اختيار الطبيب...');

    // الحصول على التخصص من URL
    const urlParams = new URLSearchParams(window.location.search);
    const specialty = urlParams.get('specialty') || 'general';

    console.log('التخصص المحدد:', specialty);

    // تحديث العنوان
    const specialtyTitle = document.getElementById('specialtyTitle');
    if (specialtyTitle && SPECIALTIES_DATABASE[specialty]) {
        specialtyTitle.textContent = `أطباء ${SPECIALTIES_DATABASE[specialty].name}`;
    }

    // تحميل الأطباء
    loadDoctorsForSpecialty(specialty);
}

/**
 * تحميل الأطباء للتخصص المحدد
 */
function loadDoctorsForSpecialty(specialty) {
    const doctorsGrid = document.getElementById('doctorsGrid');
    const nextBtn = document.getElementById('nextBtn');

    if (!doctorsGrid) {
        console.error('لم يتم العثور على شبكة الأطباء');
        return;
    }

    const specialtyData = SPECIALTIES_DATABASE[specialty];
    if (!specialtyData) {
        console.error('تخصص غير صحيح:', specialty);
        return;
    }

    const doctors = specialtyData.doctors.map(id => ({
        id,
        ...DOCTORS_DATABASE[id]
    }));

    console.log('تحميل الأطباء:', doctors);

    // إنشاء HTML للأطباء
    doctorsGrid.innerHTML = doctors.map(doctor => `
        <div class="col-md-6 col-lg-4">
            <div class="card doctor-card border-0 shadow-sm rounded-4 h-100" data-doctor="${doctor.id}">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-3">
                        <img src="${doctor.image}" alt="${doctor.name}" class="doctor-image rounded-circle me-3" style="width: 100px; height: 100px; object-fit: cover;">
                        <div>
                            <h5 class="fw-bold mb-1">${doctor.name}</h5>
                            <div class="rating mb-1 text-warning">
                                ${'★'.repeat(Math.floor(doctor.rating))} ${doctor.rating}
                            </div>
                            <small class="text-muted">${doctor.experience} سنة خبرة</small>
                        </div>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <span class="fw-bold text-primary">${doctor.price} جنيه</span>
                        <span class="badge bg-success">متاح اليوم</span>
                    </div>
                    <button class="btn btn-outline-primary w-100">اختيار الطبيب</button>
                </div>
            </div>
        </div>
    `).join('');

    // إضافة مستمعي الأحداث مع أنيميشن للأطباء
    let selectedDoctor = null;

    document.querySelectorAll('.doctor-card').forEach((card, index) => {
        // أنيميشن دخول متدرج للأطباء
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        card.style.transition = 'all 0.5s cubic-bezier(0.4, 0, 0.2, 1)';

        setTimeout(() => {
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);

        // تأثير hover للأطباء
        card.addEventListener('mouseenter', function() {
            if (!this.classList.contains('selected-doctor')) {
                this.style.transform = 'translateY(-5px) scale(1.02)';
                this.style.boxShadow = '0 12px 30px rgba(0,0,0,0.12)';
            }
        });

        card.addEventListener('mouseleave', function() {
            if (!this.classList.contains('selected-doctor')) {
                this.style.transform = 'translateY(0) scale(1)';
                this.style.boxShadow = '';
            }
        });

        card.addEventListener('click', function() {
            // إزالة التحديد السابق مع أنيميشن
            document.querySelectorAll('.doctor-card').forEach(c => {
                c.classList.remove('border-primary', 'bg-light', 'selected-doctor');
                c.style.transform = 'translateY(0) scale(1)';
                c.style.boxShadow = '';
                c.style.borderColor = '';

                // إزالة أيقونة الاختيار
                const checkIcon = c.querySelector('.doctor-check-icon');
                if (checkIcon) {
                    checkIcon.style.animation = 'fadeOut 0.3s ease-out forwards';
                    setTimeout(() => checkIcon.remove(), 300);
                }
            });

            // إضافة التحديد الجديد مع أنيميشن
            this.classList.add('border-primary', 'bg-light', 'selected-doctor');
            this.style.transform = 'translateY(-3px) scale(1.03)';
            this.style.boxShadow = '0 15px 35px rgba(13, 110, 253, 0.15)';
            this.style.borderColor = '#0d6efd';
            this.style.borderWidth = '2px';

            // إضافة أيقونة الاختيار للطبيب
            const checkIcon = document.createElement('div');
            checkIcon.className = 'doctor-check-icon position-absolute';
            checkIcon.innerHTML = '<i class="fas fa-check-circle text-primary"></i>';
            checkIcon.style.cssText = `
                top: 10px; left: 10px; z-index: 10;
                font-size: 1.5rem;
                animation: bounceIn 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
                background: white;
                border-radius: 50%;
                padding: 2px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            `;
            this.appendChild(checkIcon);

            selectedDoctor = this.dataset.doctor;

            // تفعيل زر التالي مع أنيميشن
            if (nextBtn) {
                nextBtn.disabled = false;
                nextBtn.classList.remove('btn-secondary');
                nextBtn.classList.add('btn-primary');
                nextBtn.style.animation = 'pulse 0.5s ease-in-out';
                setTimeout(() => {
                    nextBtn.style.animation = '';
                }, 500);
            }

            console.log('تم اختيار الطبيب:', selectedDoctor);
        });
    });

    // التعامل مع زر التالي
    if (nextBtn) {
        nextBtn.addEventListener('click', function() {
            if (selectedDoctor) {
                console.log('الانتقال إلى صفحة الحجز للطبيب:', selectedDoctor);
                window.location.href = `booking.html?doctor=${selectedDoctor}&specialty=${specialty}`;
            } else {
                showAlert('يرجى اختيار الطبيب أولاً', 'warning');
            }
        });
    }
}

/**
 * عرض رسالة تنبيه
 */
function showAlert(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;

    document.body.appendChild(alertDiv);

    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// إضافة CSS للأنيميشن
function addAnimationStyles() {
    const style = document.createElement('style');
    style.textContent = `
        @keyframes bounceIn {
            0% {
                opacity: 0;
                transform: scale(0.3) rotate(-10deg);
            }
            50% {
                opacity: 1;
                transform: scale(1.1) rotate(5deg);
            }
            100% {
                opacity: 1;
                transform: scale(1) rotate(0deg);
            }
        }

        @keyframes pulse {
            0% {
                opacity: 1;
                transform: scale(1);
            }
            50% {
                opacity: 0.7;
                transform: scale(1.05);
            }
            100% {
                opacity: 1;
                transform: scale(1);
            }
        }

        @keyframes fadeOut {
            0% {
                opacity: 1;
                transform: scale(1) rotate(0deg);
            }
            100% {
                opacity: 0;
                transform: scale(0.8) rotate(10deg);
            }
        }

        @keyframes gentleShake {
            0%, 100% { transform: translateY(-5px) scale(1.05) rotate(0deg); }
            25% { transform: translateY(-5px) scale(1.05) rotate(1deg); }
            75% { transform: translateY(-5px) scale(1.05) rotate(-1deg); }
        }

        @keyframes slideInUp {
            0% {
                opacity: 0;
                transform: translateY(30px);
            }
            100% {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .specialty-card {
            position: relative;
            overflow: hidden;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .specialty-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: left 0.6s;
        }

        .specialty-card:hover::before {
            left: 100%;
        }

        .specialty-card.selected {
            background: linear-gradient(135deg, #f8f9ff 0%, #e3f2fd 100%);
        }

        .pulse-effect {
            animation: pulse 2s infinite;
        }

        .check-icon i {
            background: white;
            border-radius: 50%;
            padding: 2px;
        }

        .doctor-card {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .doctor-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
    `;
    document.head.appendChild(style);
}

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    addAnimationStyles();
    initializeAppointmentSystem();
});
