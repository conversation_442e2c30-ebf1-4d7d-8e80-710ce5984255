/**
 * مساعد طبي ذكي - JavaScript Bootstrap
 * يتعامل مع الوظائف المشتركة عبر جميع الصفحات باستخدام Bootstrap 5
 */

document.addEventListener('DOMContentLoaded', function() {
    // تهيئة مكونات Bootstrap
    initializeBootstrapComponents();

    // التعامل مع إرسال النماذج
    initializeFormHandlers();

    // التعامل مع التنقل
    initializeNavigation();

    // إصلاح مشاكل نظام حجز الأطباء
    fixAppointmentSystem();
});

/**
 * Initialize Bootstrap components
 */
function initializeBootstrapComponents() {
    // Initialize tooltips
    const tooltipTriggerList = document.querySelectorAll('[data-bs-toggle="tooltip"]');
    const tooltipList = [...tooltipTriggerList].map(tooltipTriggerEl => new bootstrap.Tooltip(tooltipTriggerEl));
    
    // Initialize popovers
    const popoverTriggerList = document.querySelectorAll('[data-bs-toggle="popover"]');
    const popoverList = [...popoverTriggerList].map(popoverTriggerEl => new bootstrap.Popover(popoverTriggerEl));
}



/**
 * Initialize form handlers
 */
function initializeFormHandlers() {
    
    // Symptom form
    const symptomForm = document.getElementById('symptomForm');
    if (symptomForm) {
        symptomForm.addEventListener('submit', handleSymptomSubmit);
    }
    
    // Newsletter form
    const newsletterForms = document.querySelectorAll('form:has(input[type="email"][placeholder*="email"])');
    newsletterForms.forEach(form => {
        form.addEventListener('submit', handleNewsletterSubmit);
    });
}



/**
 * Handle symptom form submission
 */
function handleSymptomSubmit(e) {
    const symptoms = document.getElementById('symptoms').value.trim();
    
    if (!symptoms) {
        e.preventDefault();
        showAlert('Please describe your symptoms', 'warning');
        return;
    }
    
    if (symptoms.length < 10) {
        e.preventDefault();
        showAlert('Please provide more detailed symptoms (at least 10 characters)', 'warning');
        return;
    }
    
    // Show loading overlay if it exists
    const loadingOverlay = document.getElementById('loadingOverlay');
    if (loadingOverlay) {
        loadingOverlay.classList.remove('d-none');
        loadingOverlay.classList.add('d-flex');
    }
}

/**
 * Handle newsletter form submission
 */
function handleNewsletterSubmit(e) {
    e.preventDefault();
    
    const emailInput = e.target.querySelector('input[type="email"]');
    const email = emailInput.value.trim();
    
    if (!email) {
        showAlert('Please enter your email address', 'warning');
        return;
    }
    
    if (!isValidEmail(email)) {
        showAlert('Please enter a valid email address', 'warning');
        return;
    }
    
    // Simulate newsletter subscription
    showAlert('Thank you for subscribing to our newsletter!', 'success');
    emailInput.value = '';
}

/**
 * Initialize navigation functionality
 */
function initializeNavigation() {
    // Highlight current page in navigation
    const currentPage = window.location.pathname.split('/').pop() || 'index.html';
    const navLinks = document.querySelectorAll('.nav-link');
    
    navLinks.forEach(link => {
        const href = link.getAttribute('href');
        if (href === currentPage || (currentPage === '' && href === 'index.html')) {
            link.classList.add('active');
        } else {
            link.classList.remove('active');
        }
    });
    
    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

/**
 * Show alert message
 */
function showAlert(message, type = 'info') {
    // Create alert element
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;
    
    // Add to page
    document.body.appendChild(alertDiv);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

/**
 * Validate email format
 */
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * Utility function to format text
 */
function capitalizeFirstLetter(string) {
    return string.charAt(0).toUpperCase() + string.slice(1);
}

/**
 * Handle print functionality
 */
function handlePrint() {
    window.print();
}

/**
 * إصلاح مشاكل نظام حجز الأطباء
 */
function fixAppointmentSystem() {
    // إصلاح مشكلة عدم تحميل بيانات الأطباء
    if (window.location.pathname.includes('doctors.html')) {
        fixDoctorsPage();
    }

    // إصلاح مشكلة عدم تحميل بيانات الحجز
    if (window.location.pathname.includes('booking.html')) {
        fixBookingPage();
    }

    // إصلاح مشكلة عدم عرض تأكيد الحجز
    if (window.location.pathname.includes('confirmation.html')) {
        fixConfirmationPage();
    }

    // إصلاح مشكلة عدم عرض المواعيد
    if (window.location.pathname.includes('my-appointments.html')) {
        fixMyAppointmentsPage();
    }
}

/**
 * إصلاح صفحة الأطباء
 */
function fixDoctorsPage() {
    // التأكد من تحميل بيانات الأطباء بشكل صحيح
    const doctorsGrid = document.getElementById('doctorsGrid');
    if (doctorsGrid && doctorsGrid.children.length === 0) {
        // إعادة تحميل بيانات الأطباء
        setTimeout(() => {
            if (typeof loadDoctors === 'function') {
                const urlParams = new URLSearchParams(window.location.search);
                const specialty = urlParams.get('specialty') || 'general';
                loadDoctors(specialty);
            }
        }, 100);
    }
}

/**
 * إصلاح صفحة الحجز
 */
function fixBookingPage() {
    // التأكد من تحميل بيانات الطبيب
    const doctorInfo = document.getElementById('doctorInfo');
    if (doctorInfo && doctorInfo.innerHTML.trim() === '') {
        setTimeout(() => {
            const urlParams = new URLSearchParams(window.location.search);
            const doctorId = urlParams.get('doctor');
            if (doctorId && typeof loadDoctorInfo === 'function') {
                // محاولة تحميل بيانات الطبيب من localStorage أو البيانات الافتراضية
                const defaultDoctor = {
                    name: 'د. طبيب متخصص',
                    image: 'AdobeStock_132944601_Preview.jpeg',
                    price: 300
                };
                loadDoctorInfo(defaultDoctor);
            }
        }, 100);
    }

    // إصلاح مشكلة عدم عمل التقويم
    fixCalendarIssues();
}

/**
 * إصلاح مشاكل التقويم
 */
function fixCalendarIssues() {
    const dateGrid = document.getElementById('dateGrid');
    if (dateGrid && dateGrid.children.length === 0) {
        setTimeout(() => {
            if (typeof generateDates === 'function') {
                generateDates();
            } else {
                // إنشاء تقويم بسيط
                createSimpleCalendar();
            }
        }, 100);
    }
}

/**
 * إنشاء تقويم بسيط
 */
function createSimpleCalendar() {
    const dateGrid = document.getElementById('dateGrid');
    if (!dateGrid) return;

    const today = new Date();
    const dates = [];

    for (let i = 0; i < 14; i++) {
        const date = new Date(today);
        date.setDate(today.getDate() + i);
        dates.push(date);
    }

    dateGrid.innerHTML = dates.map(date => {
        const dayName = date.toLocaleDateString('ar-EG', { weekday: 'short' });
        const dayNumber = date.getDate();
        const monthName = date.toLocaleDateString('ar-EG', { month: 'short' });
        const isWeekend = date.getDay() === 5 || date.getDay() === 6;

        return `
            <div class="col-6 col-md-4 col-lg-3">
                <div class="card calendar-day ${isWeekend ? 'unavailable' : ''}" data-date="${date.toISOString().split('T')[0]}">
                    <div class="card-body text-center p-2">
                        <div class="fw-bold">${dayName}</div>
                        <div class="h5 mb-0">${dayNumber}</div>
                        <div class="small text-muted">${monthName}</div>
                    </div>
                </div>
            </div>
        `;
    }).join('');

    // إضافة مستمعي الأحداث
    addCalendarEventListeners();
}

/**
 * إضافة مستمعي أحداث التقويم
 */
function addCalendarEventListeners() {
    document.querySelectorAll('.calendar-day:not(.unavailable)').forEach(day => {
        day.addEventListener('click', function() {
            // إزالة التحديد السابق
            document.querySelectorAll('.calendar-day').forEach(d => d.classList.remove('selected'));

            // إضافة التحديد
            this.classList.add('selected');

            // تحديث المتغير العام
            if (window.selectedDate !== undefined) {
                window.selectedDate = this.dataset.date;
            }

            // إنشاء فترات زمنية
            generateTimeSlots();

            // إخفاء تنبيه عدم وجود أوقات
            const noTimesAlert = document.getElementById('noTimesAlert');
            if (noTimesAlert) {
                noTimesAlert.style.display = 'none';
            }
        });
    });
}

/**
 * إنشاء فترات زمنية
 */
function generateTimeSlots() {
    const timeSlotsContainer = document.getElementById('timeSlots');
    if (!timeSlotsContainer) return;

    const timeSlots = [
        '09:00', '09:30', '10:00', '10:30', '11:00', '11:30',
        '12:00', '12:30', '14:00', '14:30', '15:00', '15:30',
        '16:00', '16:30', '17:00', '17:30', '18:00', '18:30'
    ];

    const unavailableTimes = ['10:30', '14:30', '16:00'];

    timeSlotsContainer.innerHTML = timeSlots.map(time => {
        const isUnavailable = unavailableTimes.includes(time);
        return `
            <button class="btn btn-outline-primary time-slot ${isUnavailable ? 'unavailable' : ''}"
                    data-time="${time}" ${isUnavailable ? 'disabled' : ''}>
                ${time}
            </button>
        `;
    }).join('');

    // إضافة مستمعي الأحداث للأوقات
    addTimeSlotEventListeners();
}

/**
 * إضافة مستمعي أحداث الفترات الزمنية
 */
function addTimeSlotEventListeners() {
    document.querySelectorAll('.time-slot:not(.unavailable)').forEach(slot => {
        slot.addEventListener('click', function() {
            // إزالة التحديد السابق
            document.querySelectorAll('.time-slot').forEach(s => s.classList.remove('selected'));

            // إضافة التحديد
            this.classList.add('selected');

            // تحديث المتغير العام
            if (window.selectedTime !== undefined) {
                window.selectedTime = this.dataset.time;
            }

            // تحقق من اكتمال النموذج
            if (typeof checkFormCompletion === 'function') {
                checkFormCompletion();
            }
        });
    });
}

/**
 * إصلاح صفحة التأكيد
 */
function fixConfirmationPage() {
    // التحقق من وجود بيانات الحجز
    const bookingData = localStorage.getItem('bookingData');
    if (!bookingData) {
        // إنشاء بيانات تجريبية
        const sampleBookingData = {
            doctor: {
                name: 'د. طبيب متخصص',
                image: 'AdobeStock_132944601_Preview.jpeg',
                price: 300
            },
            date: new Date().toISOString().split('T')[0],
            time: '10:00',
            patient: {
                name: 'مريض تجريبي',
                phone: '01234567890',
                age: '30',
                gender: 'male'
            }
        };
        localStorage.setItem('bookingData', JSON.stringify(sampleBookingData));
    }
}

/**
 * إصلاح صفحة مواعيدي
 */
function fixMyAppointmentsPage() {
    const appointmentsList = document.getElementById('appointmentsList');
    if (appointmentsList && appointmentsList.children.length === 0) {
        // إضافة مواعيد تجريبية إذا لم توجد
        setTimeout(() => {
            if (typeof renderAppointments === 'function') {
                renderAppointments();
            }
        }, 100);
    }
}

/**
 * Enhanced Booking System Functions
 */

// Global variables for booking system
window.selectedDate = null;
window.selectedTime = null;
window.selectedDoctor = null;

/**
 * Load doctors data for doctors page
 */
function loadDoctors(specialty = 'general') {
    const doctorsGrid = document.getElementById('doctorsGrid');
    if (!doctorsGrid) return;

    // Sample doctors data
    const doctorsData = {
        general: [
            { id: 1, name: 'د. أحمد محمد', specialty: 'طب عام', image: 'https://images.unsplash.com/photo-1612349317150-e413f6a5b16d?w=150&h=150&fit=crop&crop=face', rating: 4.8, experience: 15, price: 300, available: true },
            { id: 2, name: 'د. فاطمة علي', specialty: 'طب عام', image: 'https://images.unsplash.com/photo-1559839734-2b71ea197ec2?w=150&h=150&fit=crop&crop=face', rating: 4.9, experience: 12, price: 280, available: true },
            { id: 3, name: 'د. محمد حسن', specialty: 'طب عام', image: 'https://images.unsplash.com/photo-1582750433449-648ed127bb54?w=150&h=150&fit=crop&crop=face', rating: 4.7, experience: 18, price: 320, available: false }
        ],
        cardiology: [
            { id: 4, name: 'د. سارة أحمد', specialty: 'أمراض القلب', image: 'https://images.unsplash.com/photo-1594824388853-e0c8b8b0b0b0?w=150&h=150&fit=crop&crop=face', rating: 4.9, experience: 20, price: 500, available: true },
            { id: 5, name: 'د. عمر محمود', specialty: 'أمراض القلب', image: 'https://images.unsplash.com/photo-1612349317150-e413f6a5b16d?w=150&h=150&fit=crop&crop=face', rating: 4.8, experience: 16, price: 450, available: true }
        ],
        dermatology: [
            { id: 6, name: 'د. نور الدين', specialty: 'الجلدية', image: 'https://images.unsplash.com/photo-1559839734-2b71ea197ec2?w=150&h=150&fit=crop&crop=face', rating: 4.7, experience: 14, price: 350, available: true },
            { id: 7, name: 'د. ليلى حسام', specialty: 'الجلدية', image: 'https://images.unsplash.com/photo-1594824388853-e0c8b8b0b0b0?w=150&h=150&fit=crop&crop=face', rating: 4.8, experience: 11, price: 380, available: true }
        ]
    };

    const doctors = doctorsData[specialty] || doctorsData.general;

    doctorsGrid.innerHTML = doctors.map(doctor => `
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card doctor-card h-100 border-0 shadow-sm rounded-4" data-doctor-id="${doctor.id}">
                <div class="selection-indicator">
                    <i class="fas fa-check-circle text-primary fa-2x"></i>
                </div>
                <div class="availability-badge ${doctor.available ? 'available' : 'busy'}">
                    ${doctor.available ? 'متاح' : 'مشغول'}
                </div>
                <div class="card-body p-4 text-center">
                    <img src="${doctor.image}" alt="${doctor.name}" class="doctor-image rounded-circle mb-3">
                    <h5 class="fw-bold mb-2">${doctor.name}</h5>
                    <p class="text-muted mb-2">${doctor.specialty}</p>
                    <div class="rating mb-2">
                        ${'★'.repeat(Math.floor(doctor.rating))} ${doctor.rating}
                    </div>
                    <p class="small text-muted mb-3">${doctor.experience} سنة خبرة</p>
                    <div class="d-flex justify-content-between align-items-center">
                        <span class="fw-bold text-primary">${doctor.price} جنيه</span>
                        <button class="btn btn-sm btn-outline-primary ${!doctor.available ? 'disabled' : ''}"
                                ${!doctor.available ? 'disabled' : ''}>
                            ${doctor.available ? 'اختيار' : 'غير متاح'}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `).join('');

    // Add click event listeners
    addDoctorSelectionListeners();
}

/**
 * Add doctor selection event listeners
 */
function addDoctorSelectionListeners() {
    document.querySelectorAll('.doctor-card:not(.disabled)').forEach(card => {
        card.addEventListener('click', function() {
            const doctorId = this.dataset.doctorId;
            selectDoctor(doctorId);
        });
    });
}

/**
 * Select a doctor
 */
function selectDoctor(doctorId) {
    // Remove previous selection
    document.querySelectorAll('.doctor-card').forEach(card => {
        card.classList.remove('selected');
    });

    // Add selection to clicked card
    const selectedCard = document.querySelector(`[data-doctor-id="${doctorId}"]`);
    if (selectedCard) {
        selectedCard.classList.add('selected');

        // Store selected doctor data
        const doctorData = extractDoctorData(selectedCard);
        window.selectedDoctor = doctorData;
        localStorage.setItem('selectedDoctor', JSON.stringify(doctorData));

        // Enable next button
        const nextBtn = document.getElementById('nextBtn');
        if (nextBtn) {
            nextBtn.disabled = false;
            nextBtn.onclick = () => {
                window.location.href = `booking.html?doctor=${doctorId}`;
            };
        }

        showAlert('تم اختيار الطبيب بنجاح!', 'success');
    }
}

/**
 * Extract doctor data from card
 */
function extractDoctorData(card) {
    const img = card.querySelector('.doctor-image');
    const name = card.querySelector('h5').textContent;
    const specialty = card.querySelector('.text-muted').textContent;
    const price = card.querySelector('.text-primary').textContent.replace(' جنيه', '');

    return {
        id: card.dataset.doctorId,
        name: name,
        specialty: specialty,
        image: img.src,
        price: parseInt(price)
    };
}

/**
 * Load doctor info for booking page
 */
function loadDoctorInfo(doctor) {
    const doctorInfo = document.getElementById('doctorInfo');
    if (!doctorInfo) return;

    doctorInfo.innerHTML = `
        <div class="d-flex align-items-center mb-3">
            <img src="${doctor.image}" alt="${doctor.name}" class="doctor-image rounded-circle me-3">
            <div>
                <h5 class="fw-bold mb-1">${doctor.name}</h5>
                <p class="text-muted mb-0">${doctor.specialty || 'طبيب متخصص'}</p>
            </div>
        </div>
        <div class="row g-3">
            <div class="col-6">
                <div class="text-center p-3 bg-light rounded">
                    <i class="fas fa-money-bill-wave text-primary mb-2"></i>
                    <div class="fw-bold">${doctor.price} جنيه</div>
                    <small class="text-muted">سعر الكشف</small>
                </div>
            </div>
            <div class="col-6">
                <div class="text-center p-3 bg-light rounded">
                    <i class="fas fa-clock text-primary mb-2"></i>
                    <div class="fw-bold">30 دقيقة</div>
                    <small class="text-muted">مدة الجلسة</small>
                </div>
            </div>
        </div>
    `;
}

/**
 * Check form completion for booking
 */
function checkFormCompletion() {
    const patientName = document.getElementById('patientName')?.value;
    const patientPhone = document.getElementById('patientPhone')?.value;
    const patientAge = document.getElementById('patientAge')?.value;
    const patientGender = document.getElementById('patientGender')?.value;

    const isFormComplete = patientName && patientPhone && patientAge && patientGender &&
                          window.selectedDate && window.selectedTime;

    const confirmBtn = document.getElementById('confirmBookingBtn');
    if (confirmBtn) {
        confirmBtn.disabled = !isFormComplete;
    }

    // Update booking summary
    if (isFormComplete) {
        updateBookingSummary();
    }
}

/**
 * Update booking summary
 */
function updateBookingSummary() {
    const summaryContainer = document.getElementById('bookingSummary');
    const summaryContent = document.getElementById('summaryContent');

    if (!summaryContent) return;

    const doctor = JSON.parse(localStorage.getItem('selectedDoctor') || '{}');
    const selectedDate = new Date(window.selectedDate);
    const formattedDate = selectedDate.toLocaleDateString('ar-EG', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });

    summaryContent.innerHTML = `
        <div class="mb-3">
            <strong>الطبيب:</strong> ${doctor.name || 'غير محدد'}
        </div>
        <div class="mb-3">
            <strong>التاريخ:</strong> ${formattedDate}
        </div>
        <div class="mb-3">
            <strong>الوقت:</strong> ${window.selectedTime}
        </div>
        <div class="mb-3">
            <strong>المريض:</strong> ${document.getElementById('patientName')?.value}
        </div>
        <hr>
        <div class="d-flex justify-content-between">
            <strong>المجموع:</strong>
            <strong class="text-primary">${doctor.price || 300} جنيه</strong>
        </div>
    `;

    if (summaryContainer) {
        summaryContainer.style.display = 'block';
    }
}

// Export functions for use in other scripts
window.AImedical = {
    showAlert,
    isValidEmail,
    capitalizeFirstLetter,
    handlePrint,
    fixAppointmentSystem,
    createSimpleCalendar,
    generateTimeSlots,
    loadDoctors,
    loadDoctorInfo,
    selectDoctor,
    checkFormCompletion,
    updateBookingSummary
};
