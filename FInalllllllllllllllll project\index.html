<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مساعد طبي ذكي - حلول رعاية صحية متقدمة</title>
    <meta name="description" content="احصل على تشخيص طبي فوري بالذكاء الاصطناعي وتوصيات صحية موثوقة. مساعدة طبية سريعة ودقيقة.">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap RTL -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css">
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;500;600;700&display=swap" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="style-bootstrap.css">
    <style>
        body { font-family: 'Cairo', sans-serif; }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-light fixed-top shadow-sm">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="index.html">
                <i class="fas fa-heartbeat text-primary me-2"></i>
                <span class="fw-bold">مساعد طبي ذكي</span>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="index.html">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="diagnosis.html">التشخيص</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="appointments.html">حجز موعد</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Login Modal -->
    <div class="modal fade" id="loginModal" tabindex="-1" aria-labelledby="loginModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content rounded-4 shadow">
                <div class="modal-header p-4 pb-0 border-bottom-0">
                    <ul class="nav nav-tabs nav-fill w-100" id="authTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="login-tab" data-bs-toggle="tab" data-bs-target="#login-tab-pane" type="button" role="tab" aria-controls="login-tab-pane" aria-selected="true">تسجيل الدخول</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="register-tab" data-bs-toggle="tab" data-bs-target="#register-tab-pane" type="button" role="tab" aria-controls="register-tab-pane" aria-selected="false">إنشاء حساب</button>
                        </li>
                    </ul>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-4 pt-0">
                    <div class="tab-content" id="authTabContent">
                        <!-- Login Form -->
                        <div class="tab-pane fade show active" id="login-tab-pane" role="tabpanel" aria-labelledby="login-tab" tabindex="0">
                            <form id="loginForm" class="mt-4">
                                <div class="mb-3">
                                    <label for="loginEmail" class="form-label">البريد الإلكتروني</label>
                                    <input type="email" class="form-control" id="loginEmail" required>
                                </div>
                                <div class="mb-3">
                                    <label for="loginPassword" class="form-label">كلمة المرور</label>
                                    <input type="password" class="form-control" id="loginPassword" required>
                                </div>
                                <button type="submit" class="btn btn-primary w-100 mt-3 rounded-pill">تسجيل الدخول</button>
                            </form>
                        </div>
                        <!-- Register Form -->
                        <div class="tab-pane fade" id="register-tab-pane" role="tabpanel" aria-labelledby="register-tab" tabindex="0">
                            <form id="registerForm" class="mt-4">
                                <div class="mb-3">
                                    <label for="registerName" class="form-label">الاسم الكامل</label>
                                    <input type="text" class="form-control" id="registerName" required>
                                </div>
                                <div class="mb-3">
                                    <label for="registerEmail" class="form-label">البريد الإلكتروني</label>
                                    <input type="email" class="form-control" id="registerEmail" required>
                                </div>
                                <div class="mb-3">
                                    <label for="registerPassword" class="form-label">كلمة المرور</label>
                                    <input type="password" class="form-control" id="registerPassword" required>
                                </div>
                                <button type="submit" class="btn btn-primary w-100 mt-3 rounded-pill">إنشاء حساب</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Hero Section -->
    <section class="hero d-flex align-items-center">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6 mb-5 mb-lg-0">
                    <h1 class="display-4 fw-bold text-white mb-3">مساعدك الطبي الذكي</h1>
                    <p class="lead text-white-50 mb-5">تحليل ذكي للأعراض باستخدام الذكاء الاصطناعي ومعالجة اللغة الطبيعية</p>
                    <div class="d-grid gap-3 d-sm-flex">
                        <a href="diagnosis.html" class="btn btn-primary btn-lg px-5 py-3 rounded-pill">ابدأ التشخيص</a>
                        <a href="appointments.html" class="btn btn-success btn-lg px-5 py-3 rounded-pill">احجز موعد</a>
                    </div>
                </div>
                <div class="col-lg-6 d-none d-lg-block">
                    <div class="hero-image-container position-relative">
                        <img src="medical-banner-with-doctor-wearing-goggles.jpg" class="img-fluid rounded-5 shadow-lg" alt="Doctor using medical technology">
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features py-5 py-md-6">
        <div class="container py-5">
            <div class="row justify-content-center text-center mb-5">
                <div class="col-lg-8">
                    <h2 class="fw-bold mb-3">لماذا تختارنا</h2>
                    <p class="lead text-muted">منصتنا المدعومة بالذكاء الاصطناعي تقدم عدة مزايا لاحتياجاتك الصحية</p>
                </div>
            </div>
            <div class="row g-4">
                <div class="col-md-6 col-lg-3">
                    <div class="card h-100 border-0 shadow-sm rounded-4 hover-lift">
                        <div class="card-body text-center p-4">
                            <div class="feature-icon bg-primary bg-gradient text-white rounded-circle mb-4">
                                <i class="fas fa-user-md"></i>
                            </div>
                            <h3 class="card-title h5 fw-bold">سهل الاستخدام</h3>
                            <p class="card-text text-muted">واجهة بسيطة لوصف أعراضك باللغة الطبيعية</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 col-lg-3">
                    <div class="card h-100 border-0 shadow-sm rounded-4 hover-lift">
                        <div class="card-body text-center p-4">
                            <div class="feature-icon bg-primary bg-gradient text-white rounded-circle mb-4">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <h3 class="card-title h5 fw-bold">دقة عالية</h3>
                            <p class="card-text text-muted">خوارزميات ذكاء اصطناعي متقدمة تقدم تحليلاً دقيقاً لأعراضك</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 col-lg-3">
                    <div class="card h-100 border-0 shadow-sm rounded-4 hover-lift">
                        <div class="card-body text-center p-4">
                            <div class="feature-icon bg-primary bg-gradient text-white rounded-circle mb-4">
                                <i class="fas fa-bolt"></i>
                            </div>
                            <h3 class="card-title h5 fw-bold">نتائج سريعة</h3>
                            <p class="card-text text-muted">احصل على تشخيص أولي فوري دون أوقات انتظار طويلة</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 col-lg-3">
                    <div class="card h-100 border-0 shadow-sm rounded-4 hover-lift">
                        <div class="card-body text-center p-4">
                            <div class="feature-icon bg-primary bg-gradient text-white rounded-circle mb-4">
                                <i class="fas fa-heart"></i>
                            </div>
                            <h3 class="card-title h5 fw-bold">خدمة مجانية</h3>
                            <p class="card-text text-muted">احصل على رؤى طبية قيمة دون أي تكلفة عليك</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- How It Works Section -->
    <section class="steps py-5 py-md-6 bg-light">
        <div class="container py-5">
            <div class="row justify-content-center text-center mb-5">
                <div class="col-lg-8">
                    <h2 class="fw-bold mb-3">كيف يعمل</h2>
                    <p class="lead text-muted">البدء مع مساعدنا الطبي الذكي أمر بسيط</p>
                </div>
            </div>
            <div class="row g-4">
                <div class="col-md-4">
                    <div class="card h-100 border-0 shadow-sm rounded-4 hover-lift">
                        <div class="card-body p-4">
                            <div class="step-number rounded-circle bg-primary text-white">1</div>
                            <h3 class="card-title h5 fw-bold mt-4">اوصف الأعراض</h3>
                            <p class="card-text text-muted">أدخل أعراضك باللغة الطبيعية، تماماً كما تخبر طبيبك</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card h-100 border-0 shadow-sm rounded-4 hover-lift">
                        <div class="card-body p-4">
                            <div class="step-number rounded-circle bg-primary text-white">2</div>
                            <h3 class="card-title h5 fw-bold mt-4">تحليل ذكي</h3>
                            <p class="card-text text-muted">ذكاؤنا الاصطناعي المتقدم يعالج وصفك ويقارنه بالمعرفة الطبية</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card h-100 border-0 shadow-sm rounded-4 hover-lift">
                        <div class="card-body p-4">
                            <div class="step-number rounded-circle bg-primary text-white">3</div>
                            <h3 class="card-title h5 fw-bold mt-4">احصل على النتائج</h3>
                            <p class="card-text text-muted">تلقى تحليلاً مفصلاً وتوصيات بناءً على أعراضك</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- QR Code Section -->
    <section class="qr-section py-5 py-md-6">
        <div class="container py-5">
            <div class="row justify-content-center text-center">
                <div class="col-lg-8">
                    <h2 class="fw-bold mb-3">امسح الكود للوصول السريع</h2>
                    <p class="lead text-muted mb-5">استخدم كاميرا هاتفك لمسح الكود والوصول المباشر للمساعد الطبي</p>

                    <div class="row justify-content-center">
                        <div class="col-md-6 col-lg-4">
                            <div class="card border-0 shadow-lg rounded-4 p-4">
                                <div class="card-body text-center">
                                    <div class="qr-code-container mb-4">
                                        <div id="qrcode" class="d-flex justify-content-center"></div>
                                    </div>
                                    <h5 class="fw-bold mb-2">مساعد طبي ذكي</h5>
                                    <p class="text-muted small mb-3">امسح للوصول المباشر</p>
                                    <div class="d-flex justify-content-center gap-2 mb-3">
                                        <span class="badge bg-primary">مجاني</span>
                                        <span class="badge bg-success">سريع</span>
                                        <span class="badge bg-info">آمن</span>
                                    </div>
                                    <button class="btn btn-outline-primary btn-sm" id="copyLinkBtn">
                                        <i class="fas fa-copy me-2"></i>نسخ الرابط
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-5">
                        <div class="col-md-4">
                            <div class="d-flex align-items-center justify-content-center mb-3">
                                <div class="feature-icon-small bg-primary bg-gradient text-white rounded-circle me-3">
                                    <i class="fas fa-mobile-alt"></i>
                                </div>
                                <div class="text-start">
                                    <h6 class="fw-bold mb-1">وصول سريع</h6>
                                    <small class="text-muted">من أي مكان</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="d-flex align-items-center justify-content-center mb-3">
                                <div class="feature-icon-small bg-success bg-gradient text-white rounded-circle me-3">
                                    <i class="fas fa-qrcode"></i>
                                </div>
                                <div class="text-start">
                                    <h6 class="fw-bold mb-1">سهل الاستخدام</h6>
                                    <small class="text-muted">مسح بسيط</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="d-flex align-items-center justify-content-center mb-3">
                                <div class="feature-icon-small bg-info bg-gradient text-white rounded-circle me-3">
                                    <i class="fas fa-shield-alt"></i>
                                </div>
                                <div class="text-start">
                                    <h6 class="fw-bold mb-1">آمن ومحمي</h6>
                                    <small class="text-muted">بياناتك محمية</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonial Section -->
    <section class="testimonials py-5 py-md-6">
        <div class="container py-5">
            <div class="row justify-content-center text-center mb-5">
                <div class="col-lg-8">
                    <h2 class="fw-bold mb-3">ماذا يقول مستخدمونا</h2>
                    <p class="lead text-muted">اقرأ عن تجارب الأشخاص الذين استخدموا مساعدنا الطبي الذكي</p>
                </div>
            </div>
            <div class="row g-4">
                <div class="col-md-4">
                    <div class="card h-100 border-0 shadow-sm rounded-4 hover-lift">
                        <div class="card-body p-4">
                            <div class="d-flex mb-4">
                                <i class="fas fa-star text-warning"></i>
                                <i class="fas fa-star text-warning"></i>
                                <i class="fas fa-star text-warning"></i>
                                <i class="fas fa-star text-warning"></i>
                                <i class="fas fa-star text-warning"></i>
                            </div>
                            <p class="card-text mb-4">"ساعدني المساعد الطبي الذكي في تحديد أعراضي بسرعة. كانت التوصيات دقيقة ووفرت علي رحلة غير ضرورية للطبيب."</p>
                            <div class="d-flex align-items-center">
                                <div class="rounded-circle bg-primary text-white testimonial-avatar">س م</div>
                                <div class="ms-3">
                                    <h6 class="mb-0 fw-bold">سارة محمد</h6>
                                    <small class="text-muted">مريضة</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card h-100 border-0 shadow-sm rounded-4 hover-lift">
                        <div class="card-body p-4">
                            <div class="d-flex mb-4">
                                <i class="fas fa-star text-warning"></i>
                                <i class="fas fa-star text-warning"></i>
                                <i class="fas fa-star text-warning"></i>
                                <i class="fas fa-star text-warning"></i>
                                <i class="fas fa-star text-warning"></i>
                            </div>
                            <p class="card-text mb-4">"كمقدم رعاية صحية، أنا معجب بدقة هذا النظام. يساعد مرضاي في الحصول على رؤى أولية قبل مواعيدهم."</p>
                            <div class="d-flex align-items-center">
                                <div class="rounded-circle bg-primary text-white testimonial-avatar">د س</div>
                                <div class="ms-3">
                                    <h6 class="mb-0 fw-bold">د. سامية</h6>
                                    <small class="text-muted">طبيب عائلة</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card h-100 border-0 shadow-sm rounded-4 hover-lift">
                        <div class="card-body p-4">
                            <div class="d-flex mb-4">
                                <i class="fas fa-star text-warning"></i>
                                <i class="fas fa-star text-warning"></i>
                                <i class="fas fa-star text-warning"></i>
                                <i class="fas fa-star text-warning"></i>
                                <i class="fas fa-star-half-alt text-warning"></i>
                            </div>
                            <p class="card-text mb-4">"الواجهة بديهية جداً، وأقدر كيف شرحت الأسباب المحتملة لأعراضي بلغة سهلة الفهم."</p>
                            <div class="d-flex align-items-center">
                                <div class="rounded-circle bg-primary text-white testimonial-avatar">م ع</div>
                                <div class="ms-3">
                                    <h6 class="mb-0 fw-bold">محمد عبدالله</h6>
                                    <small class="text-muted">مستخدم</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Call to Action -->
    <section class="cta bg-primary py-5 py-md-6">
        <div class="container py-5 text-center">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <h2 class="fw-bold text-white mb-4">هل أنت مستعد لتجربة مساعدنا الطبي الذكي؟</h2>
                    <p class="lead text-white-50 mb-5">ابدأ تشخيصك الأول - إنه مجاني ويستغرق أقل من 5 دقائق</p>
                    <a href="diagnosis.html" class="btn btn-light btn-lg px-5 py-3 rounded-pill fw-semibold">ابدأ تشخيصك</a>
                </div>
            </div>
        </div>
    </section>



    <!-- QR Code Library -->
    <script src="https://cdn.jsdelivr.net/npm/qrcode-generator@1.4.4/qrcode.js"></script>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Arabic Localization -->
    <script src="js/arabic-localization.js"></script>
    <!-- Custom JS -->
    <script src="js/main-bootstrap.js"></script>

    <!-- QR Code Generation Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            try {
                // Get current page URL
                const currentUrl = window.location.href;

                // Create QR Code using qrcode-generator library
                const qr = qrcode(0, 'M');
                qr.addData(currentUrl);
                qr.make();

                // Generate QR Code as SVG
                const qrCodeContainer = document.getElementById('qrcode');

                // Create QR Code HTML
                const cellSize = 8;
                const margin = 4;
                const size = qr.getModuleCount();
                const totalSize = (size + margin * 2) * cellSize;

                let qrHTML = `
                    <svg width="${totalSize}" height="${totalSize}" style="border-radius: 15px; box-shadow: 0 4px 15px rgba(0,123,255,0.2); border: 3px solid #f8f9fa;">
                        <rect width="${totalSize}" height="${totalSize}" fill="#ffffff"/>
                `;

                for (let row = 0; row < size; row++) {
                    for (let col = 0; col < size; col++) {
                        if (qr.isDark(row, col)) {
                            const x = (col + margin) * cellSize;
                            const y = (row + margin) * cellSize;
                            qrHTML += `<rect x="${x}" y="${y}" width="${cellSize}" height="${cellSize}" fill="#007bff"/>`;
                        }
                    }
                }

                qrHTML += '</svg>';
                qrCodeContainer.innerHTML = qrHTML;

                console.log('QR Code generated successfully!');

            } catch (error) {
                console.error('QR Code generation failed:', error);

                // Fallback: Use Google Charts API
                const currentUrl = encodeURIComponent(window.location.href);
                const qrCodeContainer = document.getElementById('qrcode');

                qrCodeContainer.innerHTML = `
                    <img src="https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${currentUrl}&color=007bff&bgcolor=ffffff"
                         alt="QR Code"
                         style="border-radius: 15px; box-shadow: 0 4px 15px rgba(0,123,255,0.2); border: 3px solid #f8f9fa; max-width: 100%; height: auto;"
                         onerror="this.parentElement.innerHTML='<div class=\\'alert alert-warning\\' style=\\'margin: 0;\\'>QR Code غير متاح حالياً<br><small>يمكنك نسخ الرابط مباشرة</small></div>'">
                `;
            }

            // Add copy link functionality
            const copyLinkBtn = document.getElementById('copyLinkBtn');
            if (copyLinkBtn) {
                copyLinkBtn.addEventListener('click', function() {
                    const currentUrl = window.location.href;

                    // Try to use the modern clipboard API
                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(currentUrl).then(function() {
                            // Success feedback
                            const originalText = copyLinkBtn.innerHTML;
                            copyLinkBtn.innerHTML = '<i class="fas fa-check me-2"></i>تم النسخ!';
                            copyLinkBtn.classList.remove('btn-outline-primary');
                            copyLinkBtn.classList.add('btn-success');

                            setTimeout(function() {
                                copyLinkBtn.innerHTML = originalText;
                                copyLinkBtn.classList.remove('btn-success');
                                copyLinkBtn.classList.add('btn-outline-primary');
                            }, 2000);
                        }).catch(function(err) {
                            console.error('Failed to copy: ', err);
                            fallbackCopyTextToClipboard(currentUrl);
                        });
                    } else {
                        // Fallback for older browsers
                        fallbackCopyTextToClipboard(currentUrl);
                    }
                });
            }

            // Fallback copy function
            function fallbackCopyTextToClipboard(text) {
                const textArea = document.createElement("textarea");
                textArea.value = text;
                textArea.style.top = "0";
                textArea.style.left = "0";
                textArea.style.position = "fixed";

                document.body.appendChild(textArea);
                textArea.focus();
                textArea.select();

                try {
                    const successful = document.execCommand('copy');
                    if (successful) {
                        const copyLinkBtn = document.getElementById('copyLinkBtn');
                        const originalText = copyLinkBtn.innerHTML;
                        copyLinkBtn.innerHTML = '<i class="fas fa-check me-2"></i>تم النسخ!';
                        copyLinkBtn.classList.remove('btn-outline-primary');
                        copyLinkBtn.classList.add('btn-success');

                        setTimeout(function() {
                            copyLinkBtn.innerHTML = originalText;
                            copyLinkBtn.classList.remove('btn-success');
                            copyLinkBtn.classList.add('btn-outline-primary');
                        }, 2000);
                    } else {
                        alert('فشل في نسخ الرابط. يرجى نسخه يدوياً: ' + text);
                    }
                } catch (err) {
                    alert('فشل في نسخ الرابط. يرجى نسخه يدوياً: ' + text);
                }

                document.body.removeChild(textArea);
            }
        });
    </script>

    <style>
        /* QR Code Section Styles */
        .qr-section {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }

        .qr-code-container {
            background: white;
            border-radius: 20px;
            padding: 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            display: inline-block;
        }

        .feature-icon-small {
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
        }

        .qr-section .card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .qr-section .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,123,255,0.15);
        }

        #qrcode canvas {
            max-width: 100%;
            height: auto;
        }

        @media (max-width: 768px) {
            .qr-code-container {
                padding: 15px;
            }

            .feature-icon-small {
                width: 40px;
                height: 40px;
                font-size: 1rem;
            }
        }
    </style>
</body>
</html>
